"use client";
import { useAuthActions } from "@convex-dev/auth/react";
import { useState } from "react";
import { toast } from "sonner";
import { SignInWithMagicLink } from "./SignInWithMagicLink";

export function SignInForm() {
  const { signIn } = useAuthActions();
  const [flow, setFlow] = useState<"signIn" | "signUp">("signIn");
  const [method, setMethod] = useState<"password" | "magicLink">("password");
  const [submitting, setSubmitting] = useState(false);

  return (
    <div className="w-full space-y-grid-6">
      {/* Form Header */}
      <div className="text-center space-y-grid-2">
        <h2 className="text-size-2 text-foreground">
          {flow === "signIn" ? "Welcome back" : "Create account"}
        </h2>
        <p className="text-size-4 text-muted">
          {flow === "signIn"
            ? "Sign in to access your partner dashboard"
            : "Join our partner program today"
          }
        </p>
      </div>

      {/* Authentication Method Toggle */}
      <div className="flex items-center justify-center">
        <div className="flex items-center bg-muted/50 rounded-lg p-1">
          <button
            type="button"
            className={`px-grid-3 py-grid-1 text-size-4 font-medium rounded-md transition-all ${
              method === "password"
                ? "bg-card text-foreground shadow-sm"
                : "text-muted hover:text-foreground"
            }`}
            onClick={() => setMethod("password")}
          >
            Password
          </button>
          <button
            type="button"
            className={`px-grid-3 py-grid-1 text-size-4 font-medium rounded-md transition-all ${
              method === "magicLink"
                ? "bg-card text-foreground shadow-sm"
                : "text-muted hover:text-foreground"
            }`}
            onClick={() => setMethod("magicLink")}
          >
            Magic Link
          </button>
        </div>
      </div>

      {/* Authentication Method Content */}
      {method === "magicLink" ? (
        <SignInWithMagicLink />
      ) : (
        <>
          {/* Password Form */}
          <form
            className="space-y-grid-4"
            onSubmit={(e) => {
              e.preventDefault();
              setSubmitting(true);
              const formData = new FormData(e.target as HTMLFormElement);
              formData.set("flow", flow);
              void signIn("password", formData).catch((error) => {
                console.log("Authentication error:", error);
                let toastTitle = "";
                if (error.message.includes("Invalid password")) {
                  toastTitle = "Invalid password. Please try again.";
                } else {
                  toastTitle =
                    flow === "signIn"
                      ? "Could not sign in, did you mean to sign up?"
                      : "Could not sign up, did you mean to sign in?";
                }
                toast.error(toastTitle);
                setSubmitting(false);
              });
            }}
          >
            {/* Email Input */}
            <div className="space-y-grid-1">
              <label htmlFor="email" className="text-size-4 font-medium text-foreground">
                Email address
              </label>
              <input
                id="email"
                className="auth-input-field"
                type="email"
                name="email"
                placeholder="Enter your email"
                required
                autoComplete="email"
              />
            </div>

            {/* Password Input */}
            <div className="space-y-grid-1">
              <label htmlFor="password" className="text-size-4 font-medium text-foreground">
                Password
              </label>
              <input
                id="password"
                className="auth-input-field"
                type="password"
                name="password"
                placeholder="Enter your password"
                required
                autoComplete={flow === "signIn" ? "current-password" : "new-password"}
              />
            </div>

            {/* Submit Button */}
            <button
              className="auth-button"
              type="submit"
              disabled={submitting}
            >
              {submitting ? (
                <div className="flex items-center justify-center gap-grid-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-accent-foreground border-t-transparent"></div>
                  <span>{flow === "signIn" ? "Signing in..." : "Creating account..."}</span>
                </div>
              ) : (
                flow === "signIn" ? "Sign in" : "Create account"
              )}
            </button>

            {/* Toggle Flow */}
            <div className="text-center">
              <p className="text-size-4 text-muted">
                {flow === "signIn"
                  ? "Don't have an account? "
                  : "Already have an account? "}
                <button
                  type="button"
                  className="text-primary hover:text-primary/80 font-medium transition-colors"
                  onClick={() => setFlow(flow === "signIn" ? "signUp" : "signIn")}
                >
                  {flow === "signIn" ? "Sign up" : "Sign in"}
                </button>
              </p>
            </div>
          </form>
        </>
      )}

      {/* Divider */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-border"></div>
        </div>
        <div className="relative flex justify-center text-size-4">
          <span className="bg-card px-grid-2 text-muted">or</span>
        </div>
      </div>

      {/* Anonymous Sign In */}
      <button
        className="btn-secondary w-full"
        onClick={() => void signIn("anonymous")}
        disabled={submitting}
      >
        <div className="flex items-center justify-center gap-grid-2">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          <span>Continue as guest</span>
        </div>
      </button>

      {/* Help Text */}
      <div className="text-center">
        <p className="text-size-4 text-muted">
          Need help? Contact your IBC Project Manager
        </p>
      </div>
    </div>
  );
}
