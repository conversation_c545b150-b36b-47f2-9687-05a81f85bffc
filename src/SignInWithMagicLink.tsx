"use client";
import { useAuthActions } from "@convex-dev/auth/react";
import { useState } from "react";
import { toast } from "sonner";

interface SignInWithMagicLinkProps {
  onSent?: () => void;
}

export function SignInWithMagicLink({ onSent }: SignInWithMagicLinkProps) {
  const { signIn } = useAuthActions();
  const [submitting, setSubmitting] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [email, setEmail] = useState("");

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSubmitting(true);
    
    const formData = new FormData(e.target as HTMLFormElement);
    const emailValue = formData.get("email") as string;
    setEmail(emailValue);
    
    try {
      await signIn("resend", formData);
      setEmailSent(true);
      onSent?.();
      toast.success("Magic link sent! Check your email.");
    } catch (error) {
      console.log("Magic link error:", error);
      toast.error("Failed to send magic link. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  if (emailSent) {
    return (
      <div className="w-full space-y-grid-6">
        <div className="text-center space-y-grid-4">
          {/* Success Icon */}
          <div className="mx-auto w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center">
            <svg 
              className="w-8 h-8 text-accent" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M3 8l7.89 7.89a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" 
              />
            </svg>
          </div>
          
          {/* Success Message */}
          <div className="space-y-grid-2">
            <h2 className="text-size-2 text-foreground">Check your email</h2>
            <p className="text-size-4 text-muted">
              We sent a magic link to <span className="font-medium text-foreground">{email}</span>
            </p>
            <p className="text-size-4 text-muted">
              Click the link in your email to sign in. The link will expire in 15 minutes.
            </p>
          </div>
        </div>

        {/* Resend Button */}
        <div className="text-center">
          <button
            type="button"
            className="text-size-4 text-primary hover:text-primary/80 font-medium transition-colors"
            onClick={() => {
              setEmailSent(false);
              setEmail("");
            }}
          >
            Send another link
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-grid-6">
      {/* Form Header */}
      <div className="text-center space-y-grid-2">
        <h2 className="text-size-2 text-foreground">Sign in with magic link</h2>
        <p className="text-size-4 text-muted">
          Enter your email and we'll send you a secure link to sign in
        </p>
      </div>

      {/* Magic Link Form */}
      <form className="space-y-grid-4" onSubmit={handleSubmit}>
        {/* Email Input */}
        <div className="space-y-grid-1">
          <label htmlFor="magic-email" className="text-size-4 font-medium text-foreground">
            Email address
          </label>
          <input
            id="magic-email"
            className="auth-input-field"
            type="email"
            name="email"
            placeholder="Enter your email"
            required
            autoComplete="email"
          />
        </div>

        {/* Submit Button */}
        <button
          className="auth-button"
          type="submit"
          disabled={submitting}
        >
          {submitting ? (
            <div className="flex items-center justify-center gap-grid-2">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-accent-foreground border-t-transparent"></div>
              <span>Sending magic link...</span>
            </div>
          ) : (
            "Send magic link"
          )}
        </button>
      </form>

      {/* Help Text */}
      <div className="text-center">
        <p className="text-size-4 text-muted">
          Don't have an account? The magic link will create one for you.
        </p>
      </div>
    </div>
  );
}