"use client";
import { useAuthActions } from "@convex-dev/auth/react";
import { useState, useEffect } from "react";
import { toast } from "sonner";

interface EmailVerificationProps {
  token?: string;
  email?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function EmailVerification({ 
  token, 
  email, 
  onSuccess, 
  onCancel 
}: EmailVerificationProps) {
  const { signIn } = useAuthActions();
  const [submitting, setSubmitting] = useState(false);
  const [isValidParams, setIsValidParams] = useState(false);

  useEffect(() => {
    // Validate that we have the required parameters
    if (token && email) {
      setIsValidParams(true);
    } else {
      // Try to get params from URL if not provided as props
      const params = new URLSearchParams(window.location.search);
      const urlToken = params.get("token");
      const urlEmail = params.get("email");
      
      if (urlToken && urlEmail) {
        setIsValidParams(true);
      }
    }
  }, [token, email]);

  const handleVerification = async () => {
    setSubmitting(true);
    
    try {
      // Get token and email from props or URL params
      const params = new URLSearchParams(window.location.search);
      const verificationToken = token || params.get("token");
      const verificationEmail = email || params.get("email");
      
      if (!verificationToken || !verificationEmail) {
        throw new Error("Missing verification parameters");
      }

      const formData = new FormData();
      formData.set("code", verificationToken);
      formData.set("email", verificationEmail);
      
      await signIn("resend", formData);
      toast.success("Successfully signed in!");
      onSuccess?.();
    } catch (error) {
      console.log("Email verification error:", error);
      toast.error("Verification failed. The link may be expired or invalid.");
    } finally {
      setSubmitting(false);
    }
  };

  if (!isValidParams) {
    return (
      <div className="w-full space-y-grid-6">
        <div className="text-center space-y-grid-4">
          {/* Error Icon */}
          <div className="mx-auto w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center">
            <svg 
              className="w-8 h-8 text-destructive" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" 
              />
            </svg>
          </div>
          
          <div className="space-y-grid-2">
            <h2 className="text-size-2 text-foreground">Invalid verification link</h2>
            <p className="text-size-4 text-muted">
              This verification link is invalid or has expired. Please request a new magic link.
            </p>
          </div>
        </div>

        <div className="text-center">
          <button
            type="button"
            className="btn-secondary"
            onClick={onCancel}
          >
            Back to sign in
          </button>
        </div>
      </div>
    );
  }

  const displayEmail = email || new URLSearchParams(window.location.search).get("email") || "";

  return (
    <div className="w-full space-y-grid-6">
      <div className="text-center space-y-grid-4">
        {/* Security Icon */}
        <div className="mx-auto w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center">
          <svg 
            className="w-8 h-8 text-accent" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" 
            />
          </svg>
        </div>
        
        {/* Verification Message */}
        <div className="space-y-grid-2">
          <h2 className="text-size-2 text-foreground">Verify your sign-in</h2>
          <p className="text-size-4 text-muted">
            You're about to sign in with the email address:
          </p>
          <p className="text-size-3 font-medium text-foreground">
            {displayEmail}
          </p>
          <p className="text-size-4 text-muted">
            Click "Continue" to complete your sign-in, or "Cancel" if this wasn't you.
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-grid-3">
        <button
          type="button"
          className="btn-secondary flex-1"
          onClick={onCancel}
          disabled={submitting}
        >
          Cancel
        </button>
        <button
          type="button"
          className="auth-button flex-1"
          onClick={handleVerification}
          disabled={submitting}
        >
          {submitting ? (
            <div className="flex items-center justify-center gap-grid-2">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-accent-foreground border-t-transparent"></div>
              <span>Verifying...</span>
            </div>
          ) : (
            "Continue"
          )}
        </button>
      </div>

      {/* Security Note */}
      <div className="text-center">
        <p className="text-size-4 text-muted">
          For your security, this verification prevents unauthorized access to your account.
        </p>
      </div>
    </div>
  );
}