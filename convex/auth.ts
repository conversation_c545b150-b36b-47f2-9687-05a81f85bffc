import { convexAuth, getAuthUserId } from "@convex-dev/auth/server";
import { Password } from "@convex-dev/auth/providers/Password";
import { Anonymous } from "@convex-dev/auth/providers/Anonymous";
import Resend from "@auth/core/providers/resend";
import { query } from "./_generated/server";
import { v } from "convex/values";

export const { auth, signIn, signOut, store, isAuthenticated } = convexAuth({
  providers: [
    Password,
    Anonymous,
    Resend({
      from: "<EMAIL>", // Update this with your verified domain
      apiKey: process.env.AUTH_RESEND_KEY,
    }),
  ],
});

export const loggedInUser = query({
  args: {},
  returns: v.union(v.null(), v.any()), // Returns user document or null
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }
    const user = await ctx.db.get(userId);
    if (!user) {
      return null;
    }
    return user;
  },
});