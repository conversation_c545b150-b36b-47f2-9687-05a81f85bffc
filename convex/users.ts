import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";
import { requireUser } from "./lib/authz";

export const myProfile = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;
    
    const user = await ctx.db.get(userId);
    return user;
  },
});

export const upsertFromAuth = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");
    
    const user = await ctx.db.get(userId);
    if (!user) {
      // Create new user record - they'll need to complete profile
      const newUserId = await ctx.db.insert("users", {
        approved: undefined, // Pending approval
        profileCompleted: false,
        role: "partner", // Default role
        tier: "trusted", // Default tier
      });
      return newUserId;
    }
    
    return user._id;
  },
});

export const completeProfile = mutation({
  args: {
    email: v.string(),
    fullName: v.string(),
    preferredCommunication: v.array(v.union(
      v.literal("whatsapp"),
      v.literal("email"),
      v.literal("telegram")
    )),
    telegram: v.optional(v.string()),
    whatsapp: v.optional(v.string()),
    emailDetails: v.optional(v.string()),
    companyName: v.string(),
    xProfile: v.optional(v.string()),
    companyType: v.string(),
    roleTitle: v.string(),
    preferredPaymentMethod: v.union(v.literal("bank"), v.literal("usdt")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    const user = await ctx.db.get(userId);
    if (!user) throw new Error("User not found");

    console.log(`🔍 User attempting to complete profile:`, {
      userId,
      email: args.email,
      currentApprovalStatus: user.approved,
      currentRole: user.role,
      profileCompleted: user.profileCompleted
    });
    
    // Generate referral code based only on full name
    const baseCode = args.fullName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '') // Remove all non-alphanumeric characters
      .substring(0, 15); // Allow longer names
    
    let referralCode = baseCode;
    let counter = 1;
    
    // Ensure uniqueness
    while (true) {
      const existing = await ctx.db
        .query("users")
        .filter((q) => q.eq(q.field("defaultReferralCode"), referralCode))
        .first();
      
      if (!existing) break;
      
      referralCode = `${baseCode}${counter}`;
      counter++;
    }
    
    console.log(`📝 Completing profile for user: ${userId} (${args.email})`);

    // Update user profile and set to pending approval
    await ctx.db.patch(userId, {
      email: args.email,
      name: args.fullName,
      fullName: args.fullName,
      preferredCommunication: args.preferredCommunication,
      telegram: args.telegram,
      whatsapp: args.whatsapp,
      emailDetails: args.emailDetails,
      companyName: args.companyName,
      xProfile: args.xProfile,
      companyType: args.companyType,
      roleTitle: args.roleTitle,
      preferredPaymentMethod: args.preferredPaymentMethod,
      defaultReferralCode: referralCode,
      profileCompleted: true,
      role: "partner", // Ensure user is marked as partner
      approved: undefined, // Set to pending approval
      termsAcceptedVersion: "1.0",
      termsAcceptedAt: Date.now(),
    });

    console.log(`✅ Profile completed for ${args.fullName} - now pending approval`);
    
    // Create default referral link pointing to the lead submission form
    await ctx.db.insert("referralLinks", {
      ownerId: userId,
      code: referralCode,
      destinationUrl: `/submit-lead?ref=${referralCode}`,
      title: `${args.fullName}'s Referral`,
    });
    
    // Log the profile completion
    await ctx.db.insert("auditLogs", {
      actorUserId: userId,
      action: "PROFILE_COMPLETED",
      entity: `users/${userId}`,
      meta: { 
        companyName: args.companyName,
        referralCode: referralCode 
      },
      at: Date.now(),
    });
    
    return { success: true, referralCode };
  },
});

export const updateTier = mutation({
  args: {
    userId: v.id("users"),
    tier: v.union(v.literal("trusted"), v.literal("elite"), v.literal("diamond")),
  },
  handler: async (ctx, { userId, tier }) => {
    const currentUser = await getAuthUserId(ctx);
    if (!currentUser) throw new Error("Not authenticated");
    
    const user = await ctx.db.get(currentUser);
    if (!user || !["admin", "superadmin", "ops"].includes(user.role || "")) {
      throw new Error("Unauthorized");
    }
    
    await ctx.db.patch(userId, { tier });
    
    await ctx.db.insert("auditLogs", {
      actorUserId: currentUser,
      action: "TIER_UPDATED",
      entity: `users/${userId}`,
      meta: { tier },
      at: Date.now(),
    });
    
    return { success: true };
  },
});

export const list = query({
  args: {},
  handler: async (ctx) => {
    const user = await requireUser(ctx, ["admin", "superadmin", "accounting", "ops"]);
    
    // Return all users for admin operations
    const users = await ctx.db.query("users").collect();
    return users;
  },
});
